<template>
  <view class="chat-container">
    <!-- 聊天消息列表 -->
    <scroll-view 
      class="message-list" 
      scroll-y="true" 
      :scroll-top="scrollTop"
      scroll-with-animation="true"
    >
      <view 
        class="message-item" 
        v-for="(message, index) in messageList" 
        :key="index"
        :class="{ 'message-right': message.isSelf }"
      >
        <!-- 时间分割线 -->
        <view v-if="message.showTime" class="time-divider">
          <text class="time-text">{{ formatMessageTime(message.timestamp) }}</text>
        </view>
        
        <!-- 消息内容 -->
        <view class="message-content">
          <!-- 头像 -->
          <image 
            class="message-avatar" 
            :src="message.isSelf ? userAvatar : chatAvatar" 
            mode="aspectFill" 
          />
          
          <!-- 消息气泡 -->
          <view class="message-bubble" :class="{ 'bubble-self': message.isSelf }">
            <text class="message-text">{{ message.content }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 输入栏 -->
    <view class="input-bar">
      <view class="input-container">
        <input 
          class="message-input" 
          type="text" 
          v-model="inputMessage"
          placeholder="输入消息..."
          placeholder-class="input-placeholder"
          @confirm="sendMessage"
        />
        <button 
          class="send-button" 
          :class="{ 'send-active': inputMessage.trim() }"
          @tap="sendMessage"
        >
          发送
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      chatId: '',
      chatName: '',
      chatAvatar: '',
      userAvatar: '/static/images/user-self.png',
      inputMessage: '',
      scrollTop: 0,
      messageList: [
        {
          id: 1,
          content: '你好！',
          timestamp: new Date().getTime() - 3600000,
          isSelf: false,
          showTime: true
        },
        {
          id: 2,
          content: '你好！有什么可以帮助你的吗？',
          timestamp: new Date().getTime() - 3500000,
          isSelf: true,
          showTime: false
        },
        {
          id: 3,
          content: '我想了解一下你的功能',
          timestamp: new Date().getTime() - 1800000,
          isSelf: false,
          showTime: true
        },
        {
          id: 4,
          content: '我可以帮你回答问题、提供建议、进行对话等。你有什么具体想了解的吗？',
          timestamp: new Date().getTime() - 1700000,
          isSelf: true,
          showTime: false
        }
      ]
    }
  },
  onLoad(options) {
    if (options.chatId) {
      this.chatId = options.chatId
    }
    if (options.name) {
      this.chatName = decodeURIComponent(options.name)
      uni.setNavigationBarTitle({
        title: this.chatName
      })
    }
    if (options.avatar) {
      this.chatAvatar = decodeURIComponent(options.avatar)
    }
    
    this.scrollToBottom()
  },
  methods: {
    sendMessage() {
      if (!this.inputMessage.trim()) return
      
      const newMessage = {
        id: this.messageList.length + 1,
        content: this.inputMessage.trim(),
        timestamp: new Date().getTime(),
        isSelf: true,
        showTime: this.shouldShowTime(new Date().getTime())
      }
      
      this.messageList.push(newMessage)
      this.inputMessage = ''
      
      this.$nextTick(() => {
        this.scrollToBottom()
      })
      
      // 模拟机器人回复
      setTimeout(() => {
        this.simulateReply()
      }, 1000)
    },
    
    simulateReply() {
      const replies = [
        '好的，我明白了',
        '这是一个很好的问题',
        '让我想想...',
        '你说得对',
        '还有其他问题吗？'
      ]
      
      const randomReply = replies[Math.floor(Math.random() * replies.length)]
      
      const replyMessage = {
        id: this.messageList.length + 1,
        content: randomReply,
        timestamp: new Date().getTime(),
        isSelf: false,
        showTime: this.shouldShowTime(new Date().getTime())
      }
      
      this.messageList.push(replyMessage)
      
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    
    shouldShowTime(timestamp) {
      if (this.messageList.length === 0) return true
      
      const lastMessage = this.messageList[this.messageList.length - 1]
      const timeDiff = timestamp - lastMessage.timestamp
      
      // 超过5分钟显示时间
      return timeDiff > 5 * 60 * 1000
    },
    
    formatMessageTime(timestamp) {
      const messageTime = new Date(timestamp)
      const now = new Date()
      
      // 今天
      if (now.toDateString() === messageTime.toDateString()) {
        return messageTime.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: false 
        })
      }
      
      // 昨天
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      if (yesterday.toDateString() === messageTime.toDateString()) {
        return '昨天 ' + messageTime.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit',
          hour12: false 
        })
      }
      
      // 更早
      return messageTime.toLocaleDateString('zh-CN') + ' ' + 
             messageTime.toLocaleTimeString('zh-CN', { 
               hour: '2-digit', 
               minute: '2-digit',
               hour12: false 
             })
    },
    
    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = 999999
      })
    }
  }
}
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F7F7F9;
}

.message-list {
  flex: 1;
  padding: 20rpx;
}

.message-item {
  margin-bottom: 32rpx;
}

.message-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.time-divider {
  text-align: center;
  margin-bottom: 24rpx;
}

.time-text {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.5);
  background-color: rgba(0, 0, 0, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.message-content {
  display: flex;
  align-items: flex-start;
  max-width: 80%;
}

.message-right .message-content {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 8rpx;
  margin: 0 16rpx;
  background-color: #f0f0f0;
}

.message-bubble {
  background-color: #ffffff;
  padding: 20rpx 24rpx;
  border-radius: 16rpx;
  max-width: 100%;
  position: relative;
}

.bubble-self {
  background-color: #00C78B;
}

.message-text {
  font-size: 30rpx;
  line-height: 1.4;
  color: rgba(0, 0, 0, 0.8);
  word-wrap: break-word;
}

.bubble-self .message-text {
  color: rgba(255, 255, 255, 1.0);
}

.input-bar {
  background-color: #ffffff;
  padding: 20rpx;
  border-top: 1rpx solid #e5e5e5;
}

.input-container {
  display: flex;
  align-items: center;
  background-color: #F7F7F9;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
}

.message-input {
  flex: 1;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.8);
  margin-right: 20rpx;
}

.input-placeholder {
  color: rgba(0, 0, 0, 0.5);
}

.send-button {
  background-color: #B9B9B9;
  color: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  transition: background-color 0.2s;
}

.send-active {
  background-color: #00C78B;
  color: rgba(255, 255, 255, 1.0);
}
</style>
