<template>
  <view class="profile-container">
    <scroll-view class="profile-content" scroll-y="true">
      <!-- 个人信息卡片 -->
      <view class="profile-header">
        <view class="user-info" @tap="editProfile">
          <image class="user-avatar" :src="userInfo.avatar" mode="aspectFill" />
          <view class="user-details">
            <text class="user-name">{{ userInfo.name }}</text>
            <text class="user-id">微信号：{{ userInfo.wechatId }}</text>
            <text class="user-status">{{ userInfo.status }}</text>
          </view>
          <view class="qr-code" @tap="showQRCode">
            <text class="qr-icon">📱</text>
          </view>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 服务功能 -->
      <view class="service-section">
        <view class="service-item" @tap="navigateTo('/pages/services/services')">
          <view class="service-icon services-icon">
            <text class="icon-text">💼</text>
          </view>
          <text class="service-name">服务</text>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 钱包、收藏 -->
      <view class="function-section">
        <view class="function-item" @tap="navigateTo('/pages/pay/pay')">
          <view class="function-icon pay-icon">
            <text class="icon-text">💳</text>
          </view>
          <text class="function-name">支付</text>
          <text class="arrow">></text>
        </view>
        
        <view class="function-item" @tap="navigateTo('/pages/favorites/favorites')">
          <view class="function-icon favorites-icon">
            <text class="icon-text">⭐</text>
          </view>
          <text class="function-name">收藏</text>
          <text class="arrow">></text>
        </view>
        
        <view class="function-item" @tap="navigateTo('/pages/moments/my-moments')">
          <view class="function-icon moments-icon">
            <text class="icon-text">📷</text>
          </view>
          <text class="function-name">朋友圈</text>
          <text class="arrow">></text>
        </view>
        
        <view class="function-item" @tap="navigateTo('/pages/cards/cards')">
          <view class="function-icon cards-icon">
            <text class="icon-text">🎫</text>
          </view>
          <text class="function-name">卡包</text>
          <text class="arrow">></text>
        </view>
        
        <view class="function-item" @tap="navigateTo('/pages/stickers/stickers')">
          <view class="function-icon stickers-icon">
            <text class="icon-text">😊</text>
          </view>
          <text class="function-name">表情</text>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 设置 -->
      <view class="settings-section">
        <view class="settings-item" @tap="navigateTo('/pages/settings/settings')">
          <view class="settings-icon">
            <text class="icon-text">⚙️</text>
          </view>
          <text class="settings-name">设置</text>
          <text class="arrow">></text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        name: '张三',
        wechatId: 'zhangsan123',
        avatar: '/static/images/user-avatar.png',
        status: '今天天气不错'
      }
    }
  },
  methods: {
    editProfile() {
      uni.navigateTo({
        url: '/pages/profile/edit'
      })
    },
    
    showQRCode() {
      uni.showToast({
        title: '显示二维码',
        icon: 'none'
      })
    },
    
    navigateTo(url) {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style scoped>
.profile-container {
  height: 100vh;
  background-color: #F7F7F9;
}

.profile-content {
  height: 100%;
}

.profile-header {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 40rpx 32rpx;
  position: relative;
}

.user-info:active {
  background-color: #f5f5f5;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 32rpx;
  background-color: #f0f0f0;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 36rpx;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 500;
  margin-bottom: 8rpx;
}

.user-id {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 8rpx;
}

.user-status {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.5);
}

.qr-code {
  margin-right: 24rpx;
  padding: 16rpx;
}

.qr-icon {
  font-size: 40rpx;
}

.arrow {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.3);
}

.service-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
}

.service-item:active {
  background-color: #f5f5f5;
}

.service-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.services-icon {
  background-color: #FFA800;
}

.service-name {
  flex: 1;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.8);
}

.function-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.function-item:last-child {
  border-bottom: none;
}

.function-item:active {
  background-color: #f5f5f5;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.pay-icon {
  background-color: #00C78B;
}

.favorites-icon {
  background-color: #FFA800;
}

.moments-icon {
  background: linear-gradient(135deg, #3EB9FF, #FFAAB4);
}

.cards-icon {
  background-color: #FF5477;
}

.stickers-icon {
  background-color: #11D180;
}

.function-name {
  flex: 1;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.8);
}

.settings-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.settings-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
}

.settings-item:active {
  background-color: #f5f5f5;
}

.settings-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  background-color: #242424;
}

.settings-name {
  flex: 1;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.8);
}

.icon-text {
  font-size: 36rpx;
  color: #ffffff;
}
</style>
