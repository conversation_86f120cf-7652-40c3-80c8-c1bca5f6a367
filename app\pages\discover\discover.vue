<template>
  <view class="discover-container">
    <scroll-view class="discover-content" scroll-y="true">
      <!-- 朋友圈 -->
      <view class="discover-section">
        <view class="discover-item" @tap="navigateTo('/pages/moments/moments')">
          <view class="item-icon moments-icon">
            <text class="icon-text">📷</text>
          </view>
          <text class="item-name">朋友圈</text>
          <view class="item-right">
            <image v-if="latestMoment" class="moment-preview" :src="latestMoment" mode="aspectFill" />
            <text class="arrow">></text>
          </view>
        </view>
      </view>
      
      <!-- 视频号、直播 -->
      <view class="discover-section">
        <view class="discover-item" @tap="navigateTo('/pages/channels/channels')">
          <view class="item-icon channels-icon">
            <text class="icon-text">📺</text>
          </view>
          <text class="item-name">视频号</text>
          <text class="arrow">></text>
        </view>
        
        <view class="discover-item" @tap="navigateTo('/pages/live/live')">
          <view class="item-icon live-icon">
            <text class="icon-text">📡</text>
          </view>
          <text class="item-name">直播</text>
          <view v-if="liveCount > 0" class="live-badge">{{ liveCount }}个朋友在直播</view>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 扫一扫、摇一摇 -->
      <view class="discover-section">
        <view class="discover-item" @tap="navigateTo('/pages/scan/scan')">
          <view class="item-icon scan-icon">
            <text class="icon-text">📱</text>
          </view>
          <text class="item-name">扫一扫</text>
          <text class="arrow">></text>
        </view>
        
        <view class="discover-item" @tap="navigateTo('/pages/shake/shake')">
          <view class="item-icon shake-icon">
            <text class="icon-text">📳</text>
          </view>
          <text class="item-name">摇一摇</text>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 看一看、搜一搜 -->
      <view class="discover-section">
        <view class="discover-item" @tap="navigateTo('/pages/topstories/topstories')">
          <view class="item-icon topstories-icon">
            <text class="icon-text">👀</text>
          </view>
          <text class="item-name">看一看</text>
          <view v-if="hasNewStories" class="red-dot"></view>
          <text class="arrow">></text>
        </view>
        
        <view class="discover-item" @tap="navigateTo('/pages/search/search')">
          <view class="item-icon search-icon">
            <text class="icon-text">🔍</text>
          </view>
          <text class="item-name">搜一搜</text>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 附近的人 -->
      <view class="discover-section">
        <view class="discover-item" @tap="navigateTo('/pages/nearby/nearby')">
          <view class="item-icon nearby-icon">
            <text class="icon-text">📍</text>
          </view>
          <text class="item-name">附近的人</text>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 购物、游戏 -->
      <view class="discover-section">
        <view class="discover-item" @tap="navigateTo('/pages/shopping/shopping')">
          <view class="item-icon shopping-icon">
            <text class="icon-text">🛒</text>
          </view>
          <text class="item-name">购物</text>
          <view class="item-subtitle">
            <text class="subtitle-text">京东购物</text>
          </view>
          <text class="arrow">></text>
        </view>
        
        <view class="discover-item" @tap="navigateTo('/pages/games/games')">
          <view class="item-icon games-icon">
            <text class="icon-text">🎮</text>
          </view>
          <text class="item-name">游戏</text>
          <text class="arrow">></text>
        </view>
      </view>
      
      <!-- 小程序 -->
      <view class="discover-section">
        <view class="discover-item" @tap="navigateTo('/pages/miniprogram/miniprogram')">
          <view class="item-icon miniprogram-icon">
            <text class="icon-text">⚡</text>
          </view>
          <text class="item-name">小程序</text>
          <text class="arrow">></text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      latestMoment: '/static/images/moment-preview.jpg',
      liveCount: 3,
      hasNewStories: true
    }
  },
  methods: {
    navigateTo(url) {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    }
  }
}
</script>

<style scoped>
.discover-container {
  height: 100vh;
  background-color: #F7F7F9;
}

.discover-content {
  height: 100%;
}

.discover-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.discover-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.discover-item:last-child {
  border-bottom: none;
}

.discover-item:active {
  background-color: #f5f5f5;
}

.item-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.moments-icon {
  background: linear-gradient(135deg, #3EB9FF, #FFAAB4);
}

.channels-icon {
  background-color: #FF5477;
}

.live-icon {
  background-color: #FFA800;
}

.scan-icon {
  background-color: #3DA2FF;
}

.shake-icon {
  background-color: #00C78B;
}

.topstories-icon {
  background-color: #11D180;
}

.search-icon {
  background-color: #FF586C;
}

.nearby-icon {
  background-color: #3DA2FF;
}

.shopping-icon {
  background-color: #FF4249;
}

.games-icon {
  background-color: #FFA800;
}

.miniprogram-icon {
  background-color: #00C78B;
}

.icon-text {
  font-size: 36rpx;
  color: #ffffff;
}

.item-name {
  flex: 1;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.8);
}

.item-right {
  display: flex;
  align-items: center;
}

.moment-preview {
  width: 60rpx;
  height: 60rpx;
  border-radius: 6rpx;
  margin-right: 16rpx;
}

.live-badge {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.5);
  margin-right: 16rpx;
}

.item-subtitle {
  margin-right: 16rpx;
}

.subtitle-text {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.5);
}

.red-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #FF4249;
  border-radius: 50%;
  margin-right: 16rpx;
}

.arrow {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.3);
}
</style>
