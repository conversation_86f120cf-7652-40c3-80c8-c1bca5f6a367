<template>
  <view class="contacts-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <text class="search-icon">🔍</text>
        <input type="text" placeholder="搜索" placeholder-class="search-placeholder" />
      </view>
    </view>
    
    <!-- 功能入口 -->
    <view class="function-section">
      <view class="function-item" @tap="navigateTo('/pages/contacts/new-friends')">
        <view class="function-icon new-friends-icon">
          <text class="icon-text">👥</text>
        </view>
        <text class="function-name">新的朋友</text>
        <view v-if="newFriendsCount > 0" class="red-dot"></view>
        <text class="arrow">></text>
      </view>
      
      <view class="function-item" @tap="navigateTo('/pages/contacts/group-chat')">
        <view class="function-icon group-chat-icon">
          <text class="icon-text">👨‍👩‍👧‍👦</text>
        </view>
        <text class="function-name">群聊</text>
        <text class="arrow">></text>
      </view>
      
      <view class="function-item" @tap="navigateTo('/pages/contacts/tags')">
        <view class="function-icon tags-icon">
          <text class="icon-text">🏷️</text>
        </view>
        <text class="function-name">标签</text>
        <text class="arrow">></text>
      </view>
      
      <view class="function-item" @tap="navigateTo('/pages/contacts/official')">
        <view class="function-icon official-icon">
          <text class="icon-text">📢</text>
        </view>
        <text class="function-name">公众号</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <!-- 联系人列表 -->
    <scroll-view class="contacts-list" scroll-y="true">
      <!-- 字母索引分组 -->
      <view v-for="(group, letter) in groupedContacts" :key="letter" class="contact-group">
        <view class="group-header">
          <text class="group-letter">{{ letter }}</text>
        </view>
        
        <view 
          v-for="(contact, index) in group" 
          :key="index"
          class="contact-item"
          @tap="openContactDetail(contact)"
        >
          <image class="contact-avatar" :src="contact.avatar" mode="aspectFill" />
          <view class="contact-info">
            <text class="contact-name">{{ contact.name }}</text>
            <text v-if="contact.remark" class="contact-remark">{{ contact.remark }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 右侧字母索引 -->
    <view class="alphabet-index">
      <text 
        v-for="letter in alphabetList" 
        :key="letter"
        class="alphabet-item"
        @tap="scrollToLetter(letter)"
      >
        {{ letter }}
      </text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      newFriendsCount: 2,
      alphabetList: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
      contactsList: [
        { name: '阿明', avatar: '/static/images/avatar1.png', remark: '', pinyin: 'aming' },
        { name: '小白', avatar: '/static/images/avatar2.png', remark: '同事', pinyin: 'xiaobai' },
        { name: '陈小红', avatar: '/static/images/avatar3.png', remark: '', pinyin: 'chenxiaohong' },
        { name: '大华', avatar: '/static/images/avatar4.png', remark: '朋友', pinyin: 'dahua' },
        { name: '二狗', avatar: '/static/images/avatar5.png', remark: '', pinyin: 'ergou' },
        { name: '方方', avatar: '/static/images/avatar6.png', remark: '同学', pinyin: 'fangfang' },
        { name: '高峰', avatar: '/static/images/avatar7.png', remark: '', pinyin: 'gaofeng' },
        { name: '黄小明', avatar: '/static/images/avatar8.png', remark: '邻居', pinyin: 'huangxiaoming' },
        { name: '李四', avatar: '/static/images/avatar9.png', remark: '', pinyin: 'lisi' },
        { name: '王五', avatar: '/static/images/avatar10.png', remark: '老板', pinyin: 'wangwu' },
        { name: '张三', avatar: '/static/images/avatar11.png', remark: '', pinyin: 'zhangsan' }
      ]
    }
  },
  computed: {
    groupedContacts() {
      const groups = {}
      
      this.contactsList.forEach(contact => {
        const firstLetter = contact.pinyin.charAt(0).toUpperCase()
        if (!groups[firstLetter]) {
          groups[firstLetter] = []
        }
        groups[firstLetter].push(contact)
      })
      
      // 按字母排序
      Object.keys(groups).forEach(letter => {
        groups[letter].sort((a, b) => a.pinyin.localeCompare(b.pinyin))
      })
      
      return groups
    }
  },
  methods: {
    navigateTo(url) {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },
    
    openContactDetail(contact) {
      uni.showToast({
        title: `打开${contact.name}的详情`,
        icon: 'none'
      })
    },
    
    scrollToLetter(letter) {
      // 这里可以实现滚动到对应字母的功能
      uni.showToast({
        title: `滚动到${letter}`,
        icon: 'none'
      })
    }
  }
}
</script>

<style scoped>
.contacts-container {
  height: 100vh;
  background-color: #F7F7F9;
  position: relative;
}

.search-bar {
  background-color: #ffffff;
  padding: 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.search-input {
  background-color: #F7F7F9;
  border-radius: 10rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
}

.search-icon {
  color: rgba(0, 0, 0, 0.5);
  margin-right: 16rpx;
  font-size: 28rpx;
}

.search-input input {
  flex: 1;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.8);
}

.search-placeholder {
  color: rgba(0, 0, 0, 0.5);
}

.function-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.function-item:last-child {
  border-bottom: none;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.new-friends-icon {
  background-color: #FFA800;
}

.group-chat-icon {
  background-color: #00C78B;
}

.tags-icon {
  background-color: #3DA2FF;
}

.official-icon {
  background-color: #FF5477;
}

.icon-text {
  font-size: 36rpx;
  color: #ffffff;
}

.function-name {
  flex: 1;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.8);
}

.red-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #FF4249;
  border-radius: 50%;
  margin-right: 16rpx;
}

.arrow {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.3);
}

.contacts-list {
  flex: 1;
  background-color: #ffffff;
  padding-right: 60rpx;
}

.contact-group {
  margin-bottom: 20rpx;
}

.group-header {
  background-color: #F7F7F9;
  padding: 12rpx 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.group-letter {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.5);
  font-weight: 500;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
}

.contact-item:active {
  background-color: #f5f5f5;
}

.contact-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
  background-color: #f0f0f0;
}

.contact-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.contact-name {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.8);
  margin-bottom: 4rpx;
}

.contact-remark {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.5);
}

.alphabet-index {
  position: fixed;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 100;
}

.alphabet-item {
  font-size: 20rpx;
  color: #00C78B;
  padding: 4rpx 8rpx;
  text-align: center;
  line-height: 1;
}
</style>
