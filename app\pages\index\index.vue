<template>
  <view class="chat-list-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input">
        <text class="search-icon">🔍</text>
        <input type="text" placeholder="搜索" placeholder-class="search-placeholder" />
      </view>
    </view>

    <!-- 朋友圈入口 -->
    <view class="moments-entry" @tap="goToMoments">
      <view class="moments-card">
        <view class="moments-icon">
          <text class="icon-text">📷</text>
        </view>
        <view class="moments-info">
          <text class="moments-title">朋友圈</text>
          <text class="moments-desc">分享生活的美好时刻</text>
        </view>
        <text class="arrow">></text>
      </view>
    </view>

    <!-- 聊天列表 -->
    <scroll-view class="chat-list" scroll-y="true">
      <view
        class="chat-item"
        v-for="(chat, index) in chatList"
        :key="index"
        @tap="openChat(chat)"
      >
        <!-- 头像 -->
        <view class="avatar-container">
          <image class="avatar" :src="chat.avatar" mode="aspectFill" />
          <view v-if="chat.unreadCount > 0" class="unread-badge">
            {{ chat.unreadCount > 99 ? '99+' : chat.unreadCount }}
          </view>
        </view>

        <!-- 聊天信息 -->
        <view class="chat-info">
          <view class="chat-header">
            <text class="chat-name">{{ chat.name }}</text>
            <text class="chat-time">{{ formatTime(chat.lastMessageTime) }}</text>
          </view>
          <view class="chat-preview">
            <text class="last-message">{{ chat.lastMessage }}</text>
            <view v-if="chat.isMuted" class="mute-icon">🔕</view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      chatList: [
        {
          id: 1,
          name: '智能助手',
          avatar: '/static/images/robot-avatar.png',
          lastMessage: '你好！有什么可以帮助你的吗？',
          lastMessageTime: new Date().getTime() - 300000, // 5分钟前
          unreadCount: 2,
          isMuted: false
        },
        {
          id: 2,
          name: '小明',
          avatar: '/static/images/user-avatar1.png',
          lastMessage: '今天天气不错呢',
          lastMessageTime: new Date().getTime() - 3600000, // 1小时前
          unreadCount: 0,
          isMuted: false
        },
        {
          id: 3,
          name: '工作群',
          avatar: '/static/images/group-avatar.png',
          lastMessage: '明天的会议改到下午3点',
          lastMessageTime: new Date().getTime() - 7200000, // 2小时前
          unreadCount: 5,
          isMuted: true
        },
        {
          id: 4,
          name: '小红',
          avatar: '/static/images/user-avatar2.png',
          lastMessage: '好的，我知道了',
          lastMessageTime: new Date().getTime() - 86400000, // 昨天
          unreadCount: 0,
          isMuted: false
        }
      ]
    }
  },
  methods: {
    openChat(chat) {
      uni.navigateTo({
        url: `/pages/chat/chat?chatId=${chat.id}&name=${chat.name}&avatar=${chat.avatar}`
      })
    },

    goToMoments() {
      uni.navigateTo({
        url: '/pages/moments/moments'
      })
    },

    formatTime(timestamp) {
      const now = new Date()
      const messageTime = new Date(timestamp)
      const diffTime = now.getTime() - messageTime.getTime()

      // 今天
      if (diffTime < 24 * 60 * 60 * 1000 && now.getDate() === messageTime.getDate()) {
        return messageTime.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        })
      }

      // 昨天
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      if (yesterday.getDate() === messageTime.getDate()) {
        return '昨天'
      }

      // 更早
      return messageTime.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.chat-list-container {
  height: 100vh;
  background-color: #F7F7F9;
  display: flex;
  flex-direction: column;
}

.search-bar {
  background-color: #ffffff;
  padding: 20rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.search-input {
  background-color: #F7F7F9;
  border-radius: 10rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
}

.search-icon {
  color: rgba(0, 0, 0, 0.5);
  margin-right: 16rpx;
  font-size: 28rpx;
}

.search-input input {
  flex: 1;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.8);
}

.search-placeholder {
  color: rgba(0, 0, 0, 0.5);
}

.moments-entry {
  background-color: #ffffff;
  margin: 20rpx 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.moments-card {
  display: flex;
  align-items: center;
  padding: 32rpx;
  transition: background-color 0.2s;
}

.moments-card:active {
  background-color: #f5f5f5;
}

.moments-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #3EB9FF, #FFAAB4);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.icon-text {
  font-size: 48rpx;
  color: #ffffff;
}

.moments-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.moments-title {
  font-size: 36rpx;
  color: rgba(0, 0, 0, 0.8);
  font-weight: 500;
  margin-bottom: 8rpx;
}

.moments-desc {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.5);
}

.arrow {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.3);
}

.chat-list {
  flex: 1;
  background-color: #ffffff;
}

.chat-item {
  display: flex;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
  transition: background-color 0.2s;
}

.chat-item:active {
  background-color: #f5f5f5;
}

.avatar-container {
  position: relative;
  margin-right: 24rpx;
}

.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 12rpx;
  background-color: #f0f0f0;
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #FF4249;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1.2;
}

.chat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 96rpx;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.chat-name {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 1.0);
  font-weight: 500;
}

.chat-time {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.5);
}

.chat-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-message {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.5);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mute-icon {
  font-size: 24rpx;
  margin-left: 16rpx;
}
</style>